import json
import os
from unittest.mock import patch

from django.conf import settings
from django.db import connection
from django.test import RequestFactory

import pytest


@pytest.fixture
def load_json():
    """Pass path from the folder tests/fixtures"""

    def wrap(path):
        with open(
            os.path.join(settings.BASE_DIR, f"accounts/tests/fixtures/{path}")
        ) as f:
            return json.load(f)

    return wrap


@pytest.fixture(autouse=True)
def media_storage(settings, tmpdir):
    settings.MEDIA_ROOT = tmpdir.strpath


@pytest.fixture
def request_factory() -> RequestFactory:
    return RequestFactory()


@pytest.fixture(scope="session", autouse=True)
def django_db_setup(django_db_blocker):

    with django_db_blocker.unblock():

        from django.apps import apps

        models_list = apps.get_models()
        for model in models_list:
            with connection.schema_editor() as schema_editor:
                schema_editor.create_model(model)

                if (
                    model._meta.db_table
                    not in connection.introspection.table_names()
                ):
                    raise ValueError(
                        "Table `{table_name}` is missing in test database.".format(
                            table_name=model._meta.db_table
                        )
                    )

        yield
        for model in models_list:
            with connection.schema_editor() as schema_editor:
                schema_editor.delete_model(model)
