import json
import pytest
import responses
from unittest.mock import patch, MagicMock
from rest_framework import status
import os

from accounts.utils.api_services.otp import OTPApiService, BaseOtpApiService
from accounts.utils.api_services.enums import OTPVia
from accounts.utils.api_services.exceptions import OTPApiException
from accounts.exceptions import ExternalAPIException


def load_fixture(filename):
    """Load JSON fixture from the fixtures directory."""
    fixture_path = os.path.join(os.path.dirname(__file__), "fixtures", filename)
    with open(fixture_path, "r") as f:
        return json.load(f)


class TestBaseOtpApiService:
    """Test cases for BaseOtpApiService."""

    def test_send_otp_not_implemented(self):
        """Test that BaseOtpApiService.send_otp raises NotImplementedError."""
        service = BaseOtpApiService()

        with pytest.raises(NotImplementedError):
            service.send_otp()


class TestOTPApiService:
    """Test cases for OTPApiService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = OTPApiService()
        self.phone_number = "**********"
        self.country_code = "91"
        self.otp = "1234"

    def test_get_url_with_trailing_slash(self):
        """Test get_url method with trailing slash."""
        service = OTPApiService()
        # Mock the BASE_URL directly
        service.BASE_URL = "http://test-api.com/"

        url = service.get_url()
        assert url == "http://test-api.com/index-v3.php"

    def test_get_url_without_trailing_slash(self):
        """Test get_url method when base URL doesn't have trailing slash."""
        service = OTPApiService()
        # Mock the BASE_URL directly
        service.BASE_URL = "http://test-api.com"

        url = service.get_url()
        assert url == "http://test-api.com/index-v3.php"

    def test_get_url_with_parseresult(self):
        """Test get_url method with ParseResult object."""
        from urllib.parse import urlparse

        service = OTPApiService()
        # Mock the BASE_URL as ParseResult
        service.BASE_URL = urlparse("http://test-api.com/")

        url = service.get_url()
        assert url == "http://test-api.com/index-v3.php"

    @responses.activate
    def test_send_otp_sms_india_success(self):
        """Test successful SMS OTP sending for India."""
        expected_response = {
            "status": "success",
            "message": "OTP sent successfully",
            "code": 200,
        }

        responses.add(
            responses.POST,
            "http://test-api.com/index-v3.php",
            json=expected_response,
            status=200,
        )

        service = OTPApiService()
        service.BASE_URL = "http://test-api.com"
        service.TIMEOUT = 20

        status_code, response = service.send_otp(
            phone_number=self.phone_number,
            country_code=self.country_code,
            otp=self.otp,
            via=OTPVia.sms,
        )

        assert status_code == 200
        assert response == expected_response

        # Verify request payload
        request_body = json.loads(responses.calls[0].request.body)
        expected_payload = {
            "template_slug": "myoperator-otp-sms",
            "app": "myoperator.otp",
            "county_code": "91",
            "send_to": "**********",
            "params": {"otp": "1234"},
        }
        assert request_body == expected_payload

    @responses.activate
    def test_send_otp_call_india_success(self):
        """Test successful Call OTP sending for India."""
        expected_response = {
            "status": "success",
            "message": "OTP call initiated successfully",
            "code": 200,
        }

        responses.add(
            responses.POST,
            "http://test-api.com/index-v3.php",
            json=expected_response,
            status=200,
        )

        service = OTPApiService()
        service.BASE_URL = "http://test-api.com"
        service.TIMEOUT = 20

        status_code, response = service.send_otp(
            phone_number=self.phone_number,
            country_code=self.country_code,
            otp=self.otp,
            via=OTPVia.call,
        )

        assert status_code == 200
        assert response == expected_response

        # Verify request payload
        request_body = json.loads(responses.calls[0].request.body)
        expected_payload = {
            "template_slug": "myoperator-otp-call",
            "app": "myoperator.otp_call",
            "county_code": "91",
            "send_to": "**********",
            "params": {"otp": "1234"},
        }
        assert request_body == expected_payload

    @responses.activate
    @patch("accounts.utils.api_services.otp.settings")
    def test_send_otp_sms_international_success(self, mock_settings):
        """Test successful SMS OTP sending for international numbers."""
        mock_settings.SMSG_API_CONFIG = {
            "HOST": "http://test-api.com",
            "TIMEOUT": 20,
        }

        # Load expected response from fixture
        otp_responses = load_fixture("otp_responses.json")
        expected_response = otp_responses["sms_success_international"]

        responses.add(
            responses.POST,
            "http://test-api.com/index-v3.php",
            json=expected_response,
            status=200,
        )

        # Create service and set the configuration directly
        service = OTPApiService()
        service.BASE_URL = "http://test-api.com"
        service.TIMEOUT = 20

        status_code, response = service.send_otp(
            phone_number="**********",
            country_code="1",  # US country code
            otp=self.otp,
            via=OTPVia.sms,
        )

        assert status_code == 200
        assert response == expected_response

        # Verify request payload for international
        request_body = json.loads(responses.calls[0].request.body)
        expected_payload = {
            "template_slug": "myoperator-otp-sms",
            "app": "myoperator.otp.international",
            "county_code": "1",
            "send_to": "**********",
            "params": {"otp": "1234"},
        }
        assert request_body == expected_payload

    @responses.activate
    @patch("accounts.utils.api_services.otp.settings")
    def test_send_otp_call_international_success(self, mock_settings):
        """Test successful Call OTP sending for international numbers."""
        mock_settings.SMSG_API_CONFIG = {
            "HOST": "http://test-api.com",
            "TIMEOUT": 20,
        }

        # Load expected response from fixture
        otp_responses = load_fixture("otp_responses.json")
        expected_response = otp_responses["call_success_international"]

        responses.add(
            responses.POST,
            "http://test-api.com/index-v3.php",
            json=expected_response,
            status=200,
        )

        # Create service and set the configuration directly
        service = OTPApiService()
        service.BASE_URL = "http://test-api.com"
        service.TIMEOUT = 20

        status_code, response = service.send_otp(
            phone_number="**********",
            country_code="1",  # US country code
            otp=self.otp,
            via=OTPVia.call,
        )

        assert status_code == 200
        assert response == expected_response

        # Verify request payload for international call
        request_body = json.loads(responses.calls[0].request.body)
        expected_payload = {
            "template_slug": "myoperator-otp-call",
            "app": "myoperator.otp_call.international",
            "county_code": "1",
            "send_to": "**********",
            "params": {"otp": "1234"},
        }
        assert request_body == expected_payload

    @responses.activate
    @patch("accounts.utils.api_services.otp.settings")
    def test_send_otp_429_rate_limit_error(self, mock_settings):
        """Test OTP sending with 429 rate limit error."""
        mock_settings.SMSG_API_CONFIG = {
            "HOST": "http://test-api.com",
            "TIMEOUT": 20,
        }

        # Load error response from fixture
        otp_responses = load_fixture("otp_responses.json")
        error_response = otp_responses["rate_limit_error"]

        responses.add(
            responses.POST,
            "http://test-api.com/index-v3.php",
            json=error_response,
            status=429,
        )

        # Create service and set the configuration directly
        service = OTPApiService()
        service.BASE_URL = "http://test-api.com"
        service.TIMEOUT = 20

        with pytest.raises(OTPApiException) as exc_info:
            service.send_otp(
                phone_number=self.phone_number,
                country_code=self.country_code,
                otp=self.otp,
                via=OTPVia.sms,
            )

        assert "sms OTP attempt limit exceeded" in str(exc_info.value)
        assert "status: 429" in str(exc_info.value)

    @responses.activate
    @patch("accounts.utils.api_services.otp.settings")
    def test_send_otp_500_server_error(self, mock_settings):
        """Test OTP sending with 500 server error."""
        mock_settings.SMSG_API_CONFIG = {
            "HOST": "http://test-api.com",
            "TIMEOUT": 20,
        }

        # Load error response from fixture
        otp_responses = load_fixture("otp_responses.json")
        error_response = otp_responses["server_error"]

        responses.add(
            responses.POST,
            "http://test-api.com/index-v3.php",
            json=error_response,
            status=500,
        )

        # Create service and set the configuration directly
        service = OTPApiService()
        service.BASE_URL = "http://test-api.com"
        service.TIMEOUT = 20

        with pytest.raises(OTPApiException) as exc_info:
            service.send_otp(
                phone_number=self.phone_number,
                country_code=self.country_code,
                otp=self.otp,
                via=OTPVia.sms,
            )

        assert "Failed to send sms OTP" in str(exc_info.value)
        assert "status: 500" in str(exc_info.value)

    @patch("accounts.utils.api_services.otp.settings")
    @patch("accounts.utils.api_services.otp.OTPApiService.post_json")
    def test_send_otp_external_api_exception(
        self, mock_post_json, mock_settings
    ):
        """Test OTP sending with ExternalAPIException."""
        mock_settings.SMSG_API_CONFIG = {
            "HOST": "http://test-api.com",
            "TIMEOUT": 20,
        }

        mock_post_json.side_effect = ExternalAPIException("Network error")

        service = OTPApiService()

        with pytest.raises(OTPApiException, match="API call failed"):
            service.send_otp(
                phone_number=self.phone_number,
                country_code=self.country_code,
                otp=self.otp,
                via=OTPVia.sms,
            )

    @patch("accounts.utils.api_services.otp.settings")
    @patch("accounts.utils.api_services.otp.OTPApiService.post_json")
    def test_send_otp_json_decode_error(self, mock_post_json, mock_settings):
        """Test OTP sending with JSON decode error."""
        mock_settings.SMSG_API_CONFIG = {
            "HOST": "http://test-api.com",
            "TIMEOUT": 20,
        }

        mock_response = MagicMock()
        mock_response.json.side_effect = json.JSONDecodeError(
            "Invalid JSON", "", 0
        )
        mock_post_json.return_value = mock_response

        service = OTPApiService()

        with pytest.raises(
            OTPApiException, match="Failed to decode JSON response"
        ):
            service.send_otp(
                phone_number=self.phone_number,
                country_code=self.country_code,
                otp=self.otp,
                via=OTPVia.sms,
            )

    def test_inheritance(self):
        """Test that OTPApiService inherits from BaseOtpApiService."""
        service = OTPApiService()
        assert isinstance(service, BaseOtpApiService)
